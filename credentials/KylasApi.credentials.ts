import {
	IAuthenticateGeneric,
	ICredentialType,
	INodeProperties,
} from 'n8n-workflow';

export class <PERSON><PERSON>sApi implements ICredentialType {
	name = 'kylasA<PERSON>';
	displayName = 'Kylas API';
	// Uses the link to this tutorial as an example
	// Replace with your own docs links when building your own nodes
	documentationUrl = 'https://docs.n8n.io/integrations/creating-nodes/build/declarative-style-node/';
	properties: INodeProperties[] = [
		{
			displayName: 'API Key',
			name: 'apiK<PERSON>',
			type: 'string',
			default: '',
			typeOptions: {
				password: true,
			},
			// password
		},
	];
	authenticate: IAuthenticateGeneric = {
		type: 'generic',
		properties: {
			headers: {
				'api-key': '={{$credentials.apiKey}}'
			}
		},
	} ;
}