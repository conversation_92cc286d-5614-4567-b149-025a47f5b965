{"name": "n8n-nodes-base.kylas", "version": "1.0.0", "description": "<PERSON>ylas n8n node", "main": "index.js", "scripts": {"build": "npx rimraf dist && tsc && gulp build:icons", "dev": "tsc --watch", "format": "prettier nodes credentials --write", "lint": "eslint nodes credentials package.json", "lintfix": "eslint nodes credentials package.json --fix", "prepublishOnly": "npm run build && npm run lint -c .eslintrc.prepublish.js nodes credentials package.json"}, "keywords": ["n8n-community-node-package", "n8n-node"], "repository": {"type": "git", "url": "git+https://github.com/kylastech/kylas_n8n.git"}, "author": {"name": "kylas technologies pvt ltd.", "email": "<EMAIL>"}, "license": "MIT", "dependencies": {"n8n-workflow": "^1.0.0"}, "files": ["dist"], "n8n": {"n8nNodesApiVersion": 1, "credentials": ["dist/credentials/KylasApi.credentials.ts"], "nodes": ["dist/nodes/kylas/Kylas.node.js"]}, "devDependencies": {"typescript": "^5.0.4"}}